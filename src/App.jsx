import { useState } from "react";

const App = () => {
  const [input, setInput] = useState("");
  const [todoList, setTodoList] = useState([]);
  const addTodo = () => {
    //add validation to prevent empty todo
    setTodoList([...todoList, { value: input, completed: false }]);
    setInput("");
  };
  const toggleTodo = (idx) => {
    const newTodoList = [...todoList];
    newTodoList[idx].completed = !newTodoList[idx].completed;
    setTodoList(newTodoList);
    setInput(""); // Clear input after adding a todo
  };

  const deleteItem = (idx) => {
    setTodoList(todoList.filter((_, i) => i !== idx));
  }
  return (
    <div>
      <input value={input} onChange={(e) => setInput(e.target.value)} />
      <button onClick={addTodo}> add </button>
      <ul>
        {todoList.map((yo, idx) => (
          <li
            key={idx}
            style={{ display: "flex", alignItems: "center", gap: "8px" }}
          >
            <span style = {{textDecoration: task.completed ? "line-through" : "none"}}>
              <input
                type="checkbox"
                checked={yo.completed}
                onChange={() => toggleTodo(idx)}
              />
              {yo.value}
            </span>
            <button onClick={() => deleteItem(idx)}> delete </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default App;
