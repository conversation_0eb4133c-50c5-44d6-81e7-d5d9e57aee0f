---
inclusion: always
---

# Code Conventions

## JavaScript/JSX Standards

### Variable Naming
- **camelCase** for variables and functions: `todoList`, `handleAddTodo`
- **PascalCase** for components: `TodoApp`, `Panel`
- **UPPER_SNAKE_CASE** for constants: `MAX_ITEMS`, `DEFAULT_COLOR`
- **Descriptive names** that explain purpose: `isEditing` not `flag`

```javascript
// ✅ Good naming
const [todoList, setTodoList] = useState([]);
const [inputValue, setInputValue] = useState("");
const handleAddTodo = () => { /* logic */ };

// ❌ Avoid unclear names
const [list, setList] = useState([]);
const [val, setVal] = useState("");
const handle = () => { /* logic */ };
```

### Function Declarations
Use function expressions for components and handlers:

```javascript
// ✅ Component declaration
const TodoApp = ({ color = "#000" }) => {
  // Component logic
};

// ✅ Handler functions
const handleAddTodo = () => {
  // Handler logic
};

// ✅ Utility functions
const generateId = () => Date.now() + Math.random();
```

### Import Organization
Group imports in this order with blank lines between groups:

```javascript
// 1. React and external libraries
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// 2. Internal components and utilities
import TodoApp from './TodoApp.jsx';
import { generateId } from './utils';

// 3. Store and actions
import store from './store';
import { addItem, updateItem } from './store';

// 4. Styles (last)
import './App.css';
```

## JSX Conventions

### Component Structure
Organize component code in this order:

```javascript
const ComponentName = ({ prop1, prop2 = defaultValue }) => {
  // 1. Hooks (useState, useEffect, etc.)
  const [state, setState] = useState(initialValue);
  const dispatch = useDispatch();
  
  // 2. Derived state and computations
  const filteredItems = items.filter(item => item.completed);
  
  // 3. Event handlers
  const handleAction = () => {
    // Handler logic
  };
  
  // 4. Effects (useEffect)
  useEffect(() => {
    // Effect logic
  }, [dependency]);
  
  // 5. Early returns for conditional rendering
  if (loading) return <div>Loading...</div>;
  
  // 6. Main render
  return (
    <div>
      {/* JSX content */}
    </div>
  );
};
```

### JSX Formatting
- Use double quotes for JSX attributes
- Use single quotes for JavaScript strings
- Self-close empty elements
- Use meaningful key props for lists

```javascript
// ✅ Good JSX formatting
return (
  <div className="todo-app">
    <input 
      type="text"
      value={inputValue}
      onChange={(e) => setInputValue(e.target.value)}
      placeholder="Add a new todo"
    />
    <button onClick={handleAddTodo}>Add Todo</button>
    <ul>
      {todos.map((todo) => (
        <li key={todo.id} className="todo-item">
          {todo.text}
        </li>
      ))}
    </ul>
  </div>
);
```

### Conditional Rendering
Use consistent patterns for conditional rendering:

```javascript
// ✅ Logical AND for simple conditions
{todos.length > 0 && (
  <ul>
    {todos.map(todo => <li key={todo.id}>{todo.text}</li>)}
  </ul>
)}

// ✅ Ternary for either/or conditions
{loading ? <Spinner /> : <TodoList items={todos} />}

// ✅ Early return for complex conditions
if (error) {
  return <ErrorMessage error={error} />;
}
```

## CSS Conventions

### Class Naming
Use BEM-inspired naming with clear hierarchy:

```css
/* Component base */
.todo-app { }

/* Component elements */
.todo-app__input { }
.todo-app__button { }
.todo-app__list { }

/* Component modifiers */
.todo-app--dark { }
.todo-app--compact { }

/* State classes */
.todo-item--completed { }
.todo-item--editing { }

/* Animation classes */
.item-enter { }
.item-exit { }
.item-update { }
```

### CSS Organization
Structure CSS files with clear sections:

```css
/* 1. Component base styles */
.todo-app {
  /* Layout properties */
  display: flex;
  flex-direction: column;
  
  /* Visual properties */
  background: #fff;
  border-radius: 8px;
  
  /* Spacing */
  padding: 16px;
  margin: 8px;
}

/* 2. Element styles */
.todo-app__input {
  /* Styles for input element */
}

/* 3. State variations */
.todo-app--dark {
  background: #333;
  color: #fff;
}

/* 4. Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
```

## Comment Standards

### Code Comments
Use comments sparingly and focus on "why" not "what":

```javascript
// ✅ Good comments explain reasoning
const handleAddTodo = () => {
  // Prevent empty todos from being added
  if (inputValue.trim()) {
    setTodos([...todos, { text: inputValue, completed: false }]);
    setInputValue(""); // Clear input after successful add
  }
};

// ❌ Avoid obvious comments
const handleAddTodo = () => {
  // Check if input value exists
  if (inputValue.trim()) {
    // Add new todo to the list
    setTodos([...todos, { text: inputValue, completed: false }]);
  }
};
```

### TODO Comments
Use consistent format for TODO comments:

```javascript
// TODO: Add input validation for maximum length
// FIXME: Handle edge case when todos array is very large
// NOTE: This animation timing matches CSS transition duration
```

## Error Handling

### Input Validation
Always validate user input:

```javascript
const handleAddTodo = () => {
  // Validate input before processing
  if (!inputValue || !inputValue.trim()) {
    return; // Early return for invalid input
  }
  
  const trimmedValue = inputValue.trim();
  if (trimmedValue.length > MAX_TODO_LENGTH) {
    setError('Todo is too long');
    return;
  }
  
  // Process valid input
  addTodo(trimmedValue);
};
```

### Safe Array Operations
Use defensive programming for array operations:

```javascript
const handleToggleTodo = (index) => {
  // Validate index bounds
  if (index < 0 || index >= todos.length) {
    console.warn('Invalid todo index:', index);
    return;
  }
  
  const newTodos = [...todos];
  newTodos[index].completed = !newTodos[index].completed;
  setTodos(newTodos);
};
```

## Performance Best Practices

### Avoid Inline Objects
Move object creation outside render when possible:

```javascript
// ✅ Stable object reference
const inputStyle = { 
  padding: '8px', 
  borderRadius: '4px' 
};

return <input style={inputStyle} />;

// ❌ Creates new object on every render
return <input style={{ padding: '8px', borderRadius: '4px' }} />;
```

### Optimize Event Handlers
Use useCallback for handlers passed to child components:

```javascript
const handleItemToggle = useCallback((id) => {
  dispatch(toggleItem(id));
}, [dispatch]);

return (
  <TodoList 
    items={todos}
    onToggle={handleItemToggle}
  />
);
```

## File Organization

### Export Patterns
Use consistent export patterns:

```javascript
// ✅ Default export for main component
const TodoApp = () => { /* component */ };
export default TodoApp;

// ✅ Named exports for utilities
export const generateId = () => Date.now();
export const validateInput = (input) => input.trim().length > 0;

// ✅ Re-exports for barrel files
export { default as TodoApp } from './TodoApp';
export { default as Panel } from './Panel';
```

### File Naming
- Components: `PascalCase.jsx` (e.g., `TodoApp.jsx`)
- Utilities: `camelCase.js` (e.g., `utils.js`, `store.js`)
- Tests: `ComponentName.test.jsx`
- Styles: `ComponentName.css`
