---
inclusion: fileMatch
fileMatchPattern: "**/store.js"
---

# Redux Patterns

## Redux Toolkit Standards

### Store Configuration
Use `configureStore` for all Redux store setup:

```javascript
import { configureStore } from '@reduxjs/toolkit';

const store = configureStore({
  reducer: {
    items: itemsSlice.reducer,
    // Add other slices here
  }
});

export default store;
```

### Slice Creation
Use `createSlice` for all reducer logic with descriptive names:

```javascript
const itemsSlice = createSlice({
  name: 'items',  // Used for action type prefixes
  initialState: [],
  reducers: {
    // Action names should be descriptive verbs
    addItem: (state, action) => {
      // Immer allows direct state mutation
      state.push({ 
        id: action.payload.id, 
        text: action.payload.text, 
        status: 'entering' 
      });
    },
    updateItem: (state, action) => {
      const item = state.find(i => i.id === action.payload.id);
      if (item) {
        item.text = action.payload.text;
        item.status = 'updating';
      }
    },
    deleteItem: (state, action) => {
      const item = state.find(i => i.id === action.payload);
      if (item) {
        item.status = 'exiting';  // Mark for animation
      }
    },
    removeItem: (state, action) => {
      // Actually remove from state after animation
      return state.filter(i => i.id !== action.payload);
    }
  }
});
```

## Action Patterns

### Action Naming
- Use descriptive verbs: `addItem`, `updateItem`, `deleteItem`
- Be specific about the operation: `setIdle` vs `setStatus`
- Group related actions in the same slice

### Action Payload Structure
Keep payloads simple and consistent:

```javascript
// ✅ Simple ID for single-item operations
dispatch(deleteItem(itemId));

// ✅ Object payload for complex operations
dispatch(addItem({ id: generateId(), text: inputValue }));
dispatch(updateItem({ id: itemId, text: newText }));
```

### Action Export Pattern
Export all actions from the slice:

```javascript
export const {
  addItem,
  updateItem,
  deleteItem,
  setIdle,
  removeItem
} = itemsSlice.actions;
```

## State Shape Design

### Normalized State Structure
Keep state flat and normalized when possible:

```javascript
// ✅ Flat array for simple cases
initialState: []

// ✅ Normalized structure for complex cases
initialState: {
  items: [],
  loading: false,
  error: null
}
```

### Item State Properties
Include animation states in item objects:

```javascript
const item = {
  id: uniqueId,
  text: "Todo text",
  completed: false,
  status: 'idle'  // 'entering', 'updating', 'exiting', 'idle'
};
```

## Animation State Management

### Lifecycle State Transitions
Manage item lifecycle through Redux actions:

```javascript
// 1. Add item with 'entering' status
dispatch(addItem({ id, text, status: 'entering' }));

// 2. Transition to 'idle' after animation
setTimeout(() => {
  dispatch(setIdle(id));
}, 400);

// 3. Mark for exit animation
dispatch(deleteItem(id));

// 4. Remove from state after animation
setTimeout(() => {
  dispatch(removeItem(id));
}, 400);
```

### Status Update Actions
Provide specific actions for status changes:

```javascript
setIdle: (state, action) => {
  const item = state.find(i => i.id === action.payload);
  if (item) {
    item.status = 'idle';
  }
}
```

## Component Integration

### useSelector Patterns
Select only the data you need:

```javascript
import { useSelector, useDispatch } from 'react-redux';

const TodoList = () => {
  const items = useSelector(state => state.items);
  const dispatch = useDispatch();
  
  // Component logic
};
```

### Dispatch Patterns
Wrap dispatch calls in meaningful handlers:

```javascript
const handleAddItem = (text) => {
  const id = generateId();
  dispatch(addItem({ id, text }));
  
  // Handle animation lifecycle
  setTimeout(() => {
    dispatch(setIdle(id));
  }, 400);
};
```

## Error Handling

### Safe State Updates
Always check for item existence before updates:

```javascript
updateItem: (state, action) => {
  const item = state.find(i => i.id === action.payload.id);
  if (item) {  // Guard against missing items
    item.text = action.payload.text;
    item.status = 'updating';
  }
}
```

### Immutable Updates
Let Immer handle immutability, but understand the patterns:

```javascript
// ✅ Immer allows direct mutation
state.push(newItem);
item.status = 'updating';

// ✅ Return new state for replacements
return state.filter(i => i.id !== action.payload);
```

## Performance Considerations

### Selector Optimization
Use specific selectors to minimize re-renders:

```javascript
// ✅ Select specific data
const activeItems = useSelector(state => 
  state.items.filter(item => !item.completed)
);

// ❌ Avoid selecting entire state
const state = useSelector(state => state);
```

### Action Batching
Group related actions when possible:

```javascript
// Consider creating compound actions for related updates
const completeItemWithAnimation = (id) => (dispatch) => {
  dispatch(updateItem({ id, completed: true }));
  dispatch(setStatus({ id, status: 'updating' }));
  setTimeout(() => {
    dispatch(setIdle(id));
  }, 400);
};
```
