---
inclusion: always
---

# Technology Stack

## Core Framework
- **React 19.1.0**: Latest React with modern hooks and concurrent features
- **React DOM 19.1.0**: Latest DOM rendering capabilities
- **React Redux 9.2.0**: Official React bindings for Redux

## State Management
- **Redux Toolkit 2.8.2**: Modern Redux with simplified patterns
  - `createSlice` for reducer logic
  - `configureStore` for store setup
  - Immer integration for immutable updates
- **React useState**: Local component state for simple cases

## Build Tools & Development
- **Vite 7.0.4**: Fast build tool and dev server
  - Hot Module Replacement (HMR)
  - Fast refresh for React components
  - ES modules support
- **@vitejs/plugin-react 4.6.0**: React integration for Vite

## Testing Framework
- **Vitest 3.2.4**: Fast unit testing framework
- **@vitest/ui 3.2.4**: Visual testing interface
- **jsdom 26.1.0**: DOM environment for testing

## Code Quality
- **ESLint 9.30.1**: JavaScript/React linting
  - `eslint-plugin-react-hooks`: React hooks rules
  - `eslint-plugin-react-refresh`: Fast refresh compatibility
- **@eslint/js**: Core ESLint configuration

## Styling Approach
- **CSS Modules**: Component-scoped styling
- **Inline Styles**: Dynamic styling for animations and themes
- **CSS Custom Properties**: Design system variables
- **Animation States**: CSS animations for item lifecycle

## Package Management
- **npm**: Primary package manager
- **package-lock.json**: Dependency version locking

## Development Environment
- **Node.js 20 LTS**: Runtime environment
- **ES Modules**: Modern JavaScript module system
- **JSX**: React component syntax

## Browser Support
- Modern browsers with ES2020+ support
- CSS Grid and Flexbox for layouts
- CSS animations and transitions

## Architecture Patterns
- **Component Composition**: Reusable UI components
- **Props-based Configuration**: Flexible component APIs
- **Redux Patterns**: Centralized state management
- **Animation State Machine**: Lifecycle-based UI states

## File Organization
- `/src`: Source code
- `/public`: Static assets
- `/dist`: Build output
- `/.kiro/steering`: Project documentation and guidance

## Development Scripts
- `npm run dev`: Development server
- `npm run build`: Production build
- `npm run lint`: Code linting
- `npm run preview`: Preview production build
