---
inclusion: fileMatch
fileMatchPattern: "**/*.jsx"
---

# Component Patterns

## React Component Standards

### Function Component Structure
Always use function declarations for components, not arrow functions assigned to variables:

```javascript
// ✅ Preferred
const TodoApp = ({ color = "#000" }) => {
  // Component logic
  return (/* JSX */);
};

// ❌ Avoid
const TodoApp = function({ color }) {
  // Component logic
};
```

### Props Patterns
- Use destructuring with default values in parameters
- Provide meaningful prop names that describe purpose
- Use TypeScript-style comments for complex props

```javascript
const TodoApp = ({ 
  color = "#000",           // Theme color for the component
  initialTodos = [],        // Starting todo items
  onTodoChange = () => {}   // Callback for todo updates
}) => {
  // Component implementation
};
```

### State Management in Components
- Use `useState` for local component state
- Group related state variables when they change together
- Use descriptive state variable names

```javascript
const TodoApp = () => {
  const [todos, setTodos] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  
  // Handlers that update multiple related states
  const handleAddTodo = () => {
    if (inputValue.trim()) {
      setTodos([...todos, { text: inputValue, completed: false }]);
      setInputValue(""); // Clear input after adding
    }
  };
};
```

### Event Handler Patterns
- Prefix handler functions with `handle`
- Use descriptive names that indicate the action
- Keep handlers focused on a single responsibility

```javascript
const handleAddTodo = () => { /* Add logic */ };
const handleToggleTodo = (index) => { /* Toggle logic */ };
const handleDeleteTodo = (index) => { /* Delete logic */ };
```

### Conditional Rendering
- Use ternary operators for simple conditions
- Use logical AND for conditional display
- Extract complex conditions to variables

```javascript
return (
  <div>
    {todos.length > 0 && (
      <ul>
        {todos.map((todo, index) => (
          <li key={index} style={{ 
            textDecoration: todo.completed ? "line-through" : "none" 
          }}>
            {todo.text}
          </li>
        ))}
      </ul>
    )}
  </div>
);
```

## Animation State Integration

### Item Lifecycle States
Components should support animation states for smooth UX:

```javascript
const addItem = (text) => {
  const newItem = { 
    id: generateId(), 
    text, 
    status: 'entering'  // Start with entering state
  };
  setItems([...items, newItem]);
  
  // Transition to idle after animation
  setTimeout(() => {
    setItemStatus(newItem.id, 'idle');
  }, 400);
};
```

### CSS Class Application
Apply animation classes based on item state:

```javascript
<li 
  className={`item ${item.status === 'entering' ? 'item-enter' : ''}`}
  key={item.id}
>
  {item.text}
</li>
```

## Reusable Component Design

### Flexible Styling Props
Allow components to be themed and customized:

```javascript
const TodoApp = ({ 
  color = "#000",
  className = "",
  style = {}
}) => {
  return (
    <div 
      className={`todo-app ${className}`}
      style={{ color, ...style }}
    >
      {/* Component content */}
    </div>
  );
};
```

### Composition Over Configuration
Design components to be composed together:

```javascript
// Panel.jsx - Composing multiple TodoApp instances
const Panel = () => (
  <div className="panel">
    <h2>Todo Panel</h2>
    <TodoApp color="#1976d2" />
    <h3>Small todo</h3>
    <TodoApp color="#e91e63" />
  </div>
);
```

## Error Handling Patterns

### Input Validation
Always validate user input before processing:

```javascript
const handleAddTodo = () => {
  if (inputValue.trim()) {  // Prevent empty todos
    setTodos([...todos, { text: inputValue, completed: false }]);
    setInputValue("");
  }
};
```

### Safe Array Operations
Use safe array methods and check for existence:

```javascript
const handleToggleTodo = (index) => {
  if (index >= 0 && index < todos.length) {
    const newTodos = [...todos];
    newTodos[index].completed = !newTodos[index].completed;
    setTodos(newTodos);
  }
};
```

## Performance Considerations

### Key Props for Lists
Always provide stable keys for list items:

```javascript
// ✅ Use stable IDs when available
{todos.map((todo) => (
  <li key={todo.id}>{todo.text}</li>
))}

// ⚠️ Use index only when items don't reorder
{todos.map((todo, index) => (
  <li key={index}>{todo.text}</li>
))}
```

### Avoid Inline Object Creation
Move object creation outside render when possible:

```javascript
// ✅ Preferred - stable reference
const itemStyle = { display: "flex", alignItems: "center", gap: "8px" };

return (
  <li style={itemStyle}>
    {/* Content */}
  </li>
);
```
