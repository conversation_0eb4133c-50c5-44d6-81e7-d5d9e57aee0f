---
inclusion: fileMatch
fileMatchPattern: "**/*.test.*"
---

# Testing Approach

## Testing Philosophy
Follow Test-Driven Development (TDD) principles. Write tests first to define expected behavior, then implement features to make tests pass. Focus on testing user behavior rather than implementation details.

## Testing Framework Setup

### Vitest Configuration
Use Vitest for fast, modern testing:

```javascript
// vitest.config.js
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'jsdom', // DOM environment for React testing
    globals: true,        // Global test functions
  }
});
```

### Test File Organization
- Place test files alongside components: `Component.test.jsx`
- Use descriptive test file names that match component names
- Group related tests in describe blocks

## Test Structure Standards

### Basic Test Template
```javascript
import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ComponentName from './ComponentName';

describe('ComponentName', () => {
  it('should render with default props', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
  
  it('should handle user interactions', () => {
    render(<ComponentName />);
    const button = screen.getByRole('button', { name: /add/i });
    fireEvent.click(button);
    expect(screen.getByText('New Item')).toBeInTheDocument();
  });
});
```

### Redux Component Testing
Test components connected to Redux store:

```javascript
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import itemsSlice from './store';

const createTestStore = (initialState = []) => {
  return configureStore({
    reducer: {
      items: itemsSlice.reducer
    },
    preloadedState: {
      items: initialState
    }
  });
};

const renderWithStore = (component, initialState) => {
  const store = createTestStore(initialState);
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('TodoApp with Redux', () => {
  it('should display items from store', () => {
    const initialItems = [
      { id: 1, text: 'Test todo', status: 'idle' }
    ];
    
    renderWithStore(<TodoApp />, initialItems);
    expect(screen.getByText('Test todo')).toBeInTheDocument();
  });
});
```

## Testing Patterns

### User-Centric Testing
Focus on what users see and do:

```javascript
describe('Todo functionality', () => {
  it('should add a new todo when user types and clicks add', () => {
    render(<TodoApp />);
    
    // User types in input
    const input = screen.getByPlaceholderText(/add a new todo/i);
    fireEvent.change(input, { target: { value: 'New task' } });
    
    // User clicks add button
    const addButton = screen.getByRole('button', { name: /add todo/i });
    fireEvent.click(addButton);
    
    // User sees the new todo
    expect(screen.getByText('New task')).toBeInTheDocument();
  });
  
  it('should toggle todo completion when checkbox is clicked', () => {
    render(<TodoApp />);
    
    // Setup: Add a todo first
    const input = screen.getByPlaceholderText(/add a new todo/i);
    fireEvent.change(input, { target: { value: 'Test task' } });
    fireEvent.click(screen.getByRole('button', { name: /add todo/i }));
    
    // User clicks checkbox
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    
    // Todo should be marked as completed
    expect(checkbox).toBeChecked();
  });
});
```

### Animation State Testing
Test animation states and transitions:

```javascript
describe('Animation states', () => {
  it('should set entering status when item is added', () => {
    const store = createTestStore();
    renderWithStore(<TodoApp />, []);
    
    // Add item
    const input = screen.getByPlaceholderText(/add a new todo/i);
    fireEvent.change(input, { target: { value: 'New task' } });
    fireEvent.click(screen.getByRole('button', { name: /add todo/i }));
    
    // Check that item has entering class
    const todoItem = screen.getByText('New task').closest('li');
    expect(todoItem).toHaveClass('item-enter');
  });
  
  it('should transition to idle state after animation', async () => {
    // Test animation state transitions with timers
    vi.useFakeTimers();
    
    render(<TodoApp />);
    
    // Add item
    fireEvent.change(screen.getByPlaceholderText(/add a new todo/i), 
      { target: { value: 'Test' } });
    fireEvent.click(screen.getByRole('button', { name: /add todo/i }));
    
    // Fast-forward time
    vi.advanceTimersByTime(400);
    
    // Item should be in idle state
    const todoItem = screen.getByText('Test').closest('li');
    expect(todoItem).not.toHaveClass('item-enter');
    
    vi.useRealTimers();
  });
});
```

### Props and Configuration Testing
Test component flexibility:

```javascript
describe('TodoApp configuration', () => {
  it('should apply custom color prop', () => {
    render(<TodoApp color="#ff0000" />);
    const container = screen.getByText('Todo App').closest('div');
    expect(container).toHaveStyle({ color: '#ff0000' });
  });
  
  it('should handle empty initial state', () => {
    render(<TodoApp />);
    expect(screen.queryByRole('listitem')).not.toBeInTheDocument();
  });
});
```

## Redux Testing

### Action Testing
Test Redux actions and reducers:

```javascript
import { addItem, updateItem, deleteItem } from './store';

describe('Items slice', () => {
  it('should add item with entering status', () => {
    const initialState = [];
    const action = addItem({ id: 1, text: 'Test' });
    const newState = itemsSlice.reducer(initialState, action);
    
    expect(newState).toHaveLength(1);
    expect(newState[0]).toEqual({
      id: 1,
      text: 'Test',
      status: 'entering'
    });
  });
  
  it('should update existing item', () => {
    const initialState = [
      { id: 1, text: 'Original', status: 'idle' }
    ];
    const action = updateItem({ id: 1, text: 'Updated' });
    const newState = itemsSlice.reducer(initialState, action);
    
    expect(newState[0].text).toBe('Updated');
    expect(newState[0].status).toBe('updating');
  });
});
```

## Error Handling Tests

### Input Validation Testing
Test edge cases and error conditions:

```javascript
describe('Input validation', () => {
  it('should not add empty todos', () => {
    render(<TodoApp />);
    
    // Try to add empty todo
    const addButton = screen.getByRole('button', { name: /add todo/i });
    fireEvent.click(addButton);
    
    // No todo should be added
    expect(screen.queryByRole('listitem')).not.toBeInTheDocument();
  });
  
  it('should trim whitespace from input', () => {
    render(<TodoApp />);
    
    const input = screen.getByPlaceholderText(/add a new todo/i);
    fireEvent.change(input, { target: { value: '   Test   ' } });
    fireEvent.click(screen.getByRole('button', { name: /add todo/i }));
    
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

## Test Utilities

### Custom Render Helpers
Create reusable test utilities:

```javascript
// test-utils.js
export const renderWithRedux = (component, initialState = {}) => {
  const store = configureStore({
    reducer: { items: itemsSlice.reducer },
    preloadedState: initialState
  });
  
  return {
    ...render(<Provider store={store}>{component}</Provider>),
    store
  };
};

export const createMockTodo = (overrides = {}) => ({
  id: Math.random(),
  text: 'Test todo',
  completed: false,
  status: 'idle',
  ...overrides
});
```

## Performance Testing

### Render Performance
Test that components don't re-render unnecessarily:

```javascript
describe('Performance', () => {
  it('should not re-render when unrelated props change', () => {
    const renderSpy = vi.fn();
    const TestComponent = ({ color, unrelatedProp }) => {
      renderSpy();
      return <TodoApp color={color} />;
    };
    
    const { rerender } = render(
      <TestComponent color="#000" unrelatedProp="a" />
    );
    
    rerender(<TestComponent color="#000" unrelatedProp="b" />);
    
    // Should only render twice (initial + rerender)
    expect(renderSpy).toHaveBeenCalledTimes(2);
  });
});
```
