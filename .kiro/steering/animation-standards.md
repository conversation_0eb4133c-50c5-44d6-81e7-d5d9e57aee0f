---
inclusion: fileMatch
fileMatchPattern: "**/*.css"
---

# Animation Standards

## Animation Philosophy
Animations should feel natural and provide meaningful feedback to users. Follow the principle of "magical touch" - animations should be delightful but not distracting.

## Animation States System

### Item Lifecycle States
Every interactive item should support these animation states:

- **`entering`**: Items being added to the interface
- **`updating`**: Items being modified or changed
- **`exiting`**: Items being removed from the interface  
- **`idle`**: Items in their normal, stable state

### CSS Class Naming
Use consistent naming for animation classes:

```css
.item-enter { /* Entry animations */ }
.item-exit { /* Exit animations */ }
.item-update { /* Update feedback */ }
.item-idle { /* Normal state */ }
```

## Animation Timing

### Duration Standards
- **Fast interactions**: 200-300ms (hover, focus)
- **Standard transitions**: 400ms (enter/exit)
- **Complex animations**: 600-800ms (multi-step)

```css
.item-enter {
  animation: itemEnter 0.4s;
}

.item-exit {
  animation: itemExit 0.4s forwards;
}

.item-update {
  animation: itemUpdate 0.4s;
}
```

### Easing Functions
Use natural easing for organic feel:

```css
/* Preferred easing curves */
transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Material Design */
transition: all 0.4s ease-out; /* Standard ease-out */
```

## Entry Animations

### Scale and Fade Pattern
Items should appear with subtle scale and fade:

```css
@keyframes itemEnter {
  from { 
    transform: scale(0.95) translateY(10px); 
    opacity: 0.2; 
  }
  to { 
    transform: scale(1) translateY(0); 
    opacity: 1; 
  }
}

.item-enter {
  animation: itemEnter 0.4s;
  box-shadow: 0 2px 8px rgba(99,102,241,0.12); /* Subtle highlight */
}
```

### Stagger Animations
For multiple items, consider staggered entry:

```css
.item-enter:nth-child(1) { animation-delay: 0ms; }
.item-enter:nth-child(2) { animation-delay: 50ms; }
.item-enter:nth-child(3) { animation-delay: 100ms; }
```

## Exit Animations

### Fade and Scale Out
Items should exit gracefully:

```css
@keyframes itemExit {
  to { 
    transform: scale(0.95) translateY(10px); 
    opacity: 0; 
  }
}

.item-exit {
  animation: itemExit 0.4s forwards;
  opacity: 0.5; /* Immediate visual feedback */
}
```

## Update Animations

### Background Flash Pattern
Provide visual feedback for updates:

```css
@keyframes itemUpdate {
  from { background: #d1fae5; } /* Light green flash */
  to { background: #fff; }
}

.item-update {
  animation: itemUpdate 0.4s;
  box-shadow: 0 2px 8px rgba(16,185,129,0.12); /* Green highlight */
}
```

## Hover and Focus States

### Interactive Feedback
Provide immediate feedback for interactive elements:

```css
.btn {
  transition: background 0.2s, color 0.2s, transform 0.1s;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
  transform: translateY(0);
}
```

### Logo and Icon Animations
Special elements can have playful animations:

```css
.logo {
  transition: transform 0.2s;
}

.logo:hover {
  transform: scale(1.1);
}
```

## Color Transitions

### Theme-Based Colors
Support smooth color transitions for theming:

```css
.todo-app {
  transition: color 0.3s ease;
}

/* Dynamic color application */
.todo-app[data-color="blue"] {
  color: #1976d2;
}

.todo-app[data-color="pink"] {
  color: #e91e63;
}
```

## Performance Considerations

### GPU Acceleration
Use transform and opacity for smooth animations:

```css
/* ✅ GPU-accelerated properties */
transform: translateY(10px) scale(0.95);
opacity: 0;

/* ❌ Avoid animating layout properties */
/* height, width, top, left */
```

### Animation Cleanup
Ensure animations don't interfere with layout:

```css
.item-exit {
  animation: itemExit 0.4s forwards;
  pointer-events: none; /* Prevent interaction during exit */
}
```

## Accessibility

### Respect User Preferences
Honor reduced motion preferences:

```css
@media (prefers-reduced-motion: reduce) {
  .item-enter,
  .item-exit,
  .item-update {
    animation: none;
    transition: none;
  }
  
  /* Provide instant feedback instead */
  .item-enter { opacity: 1; }
  .item-exit { opacity: 0; }
}
```

### Focus Indicators
Maintain clear focus indicators:

```css
.btn:focus,
.btn:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
  outline-offset: 2px;
}
```

## Animation Coordination

### JavaScript Integration
Coordinate animations with state changes:

```javascript
// Add item with entering state
dispatch(addItem({ id, text, status: 'entering' }));

// Transition to idle after animation completes
setTimeout(() => {
  dispatch(setIdle(id));
}, 400); // Match CSS animation duration
```

### State-Driven Classes
Apply classes based on item state:

```javascript
<li className={`item ${getAnimationClass(item.status)}`}>
  {item.text}
</li>

const getAnimationClass = (status) => {
  switch(status) {
    case 'entering': return 'item-enter';
    case 'exiting': return 'item-exit';
    case 'updating': return 'item-update';
    default: return '';
  }
};
```
