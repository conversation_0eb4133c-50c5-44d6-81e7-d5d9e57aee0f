---
inclusion: always
---

# Project Structure

## File Organization

```
React-ShuHaRi/
├── .kiro/steering/          # Project guidance and documentation
├── src/                     # Source code
│   ├── main.jsx            # Application entry point
│   ├── App.jsx             # Basic todo implementation
│   ├── TodoApp.jsx         # Reusable todo component
│   ├── Panel.jsx           # Multi-instance demo
│   ├── store.js            # Redux store configuration
│   ├── App.css             # Component styles
│   ├── index.css           # Global styles
│   ├── App.test.jsx        # Test files
│   └── assets/             # Static assets
├── public/                  # Public assets
├── dist/                   # Build output
├── package.json            # Dependencies and scripts
├── vite.config.js          # Vite configuration
├── vitest.config.js        # Testing configuration
└── eslint.config.js        # Linting configuration
```

## Naming Conventions

### Files
- **Components**: PascalCase (e.g., `TodoApp.jsx`, `Panel.jsx`)
- **Utilities**: camelCase (e.g., `store.js`)
- **Tests**: Component name + `.test.jsx` (e.g., `App.test.jsx`)
- **Styles**: Component name + `.css` (e.g., `App.css`)

### Components
- **React Components**: PascalCase function declarations
- **Props**: camelCase with descriptive names
- **State Variables**: camelCase with clear intent

### CSS Classes
- **BEM-inspired**: `.component-element--modifier`
- **Utility Classes**: `.btn`, `.input`, `.card`
- **Animation States**: `.item-enter`, `.item-exit`, `.item-update`

## Import Patterns

### React Imports
```javascript
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
```

### Component Imports
```javascript
import ComponentName from './ComponentName.jsx';
import { NamedExport } from './utilities';
```

### Store Imports
```javascript
import store from './store';
import { actionName } from './store';
```

## Component Architecture

### Basic Structure
```javascript
const ComponentName = ({ prop1, prop2 = defaultValue }) => {
  // Hooks at the top
  const [state, setState] = useState(initialValue);
  
  // Event handlers
  const handleAction = () => {
    // Handler logic
  };
  
  // Render
  return (
    <div>
      {/* JSX content */}
    </div>
  );
};

export default ComponentName;
```

### Redux Integration
```javascript
import { useSelector, useDispatch } from 'react-redux';
import { actionName } from './store';

const Component = () => {
  const items = useSelector(state => state.items);
  const dispatch = useDispatch();
  
  const handleAction = (payload) => {
    dispatch(actionName(payload));
  };
  
  return (/* JSX */);
};
```

## State Management Patterns

### Local State (useState)
- Simple component state
- Form inputs and UI state
- Temporary data that doesn't need sharing

### Redux State (Redux Toolkit)
- Shared application state
- Complex state logic
- State that needs persistence or time-travel debugging

### Animation States
Items have lifecycle states for smooth UX:
- `entering`: New items being added
- `updating`: Items being modified  
- `exiting`: Items being removed
- `idle`: Items in normal state

## Styling Architecture

### CSS Organization
- Global styles in `index.css`
- Component styles in `ComponentName.css`
- Inline styles for dynamic values

### Animation Classes
- `.item-enter`: Entry animations
- `.item-exit`: Exit animations
- `.item-update`: Update feedback
- Keyframe animations for smooth transitions

## Testing Structure
- Test files alongside components
- Descriptive test names
- Focus on user behavior over implementation details

## Build Configuration
- Vite for fast development and building
- ESLint for code quality
- Modern JavaScript features (ES2020+)
- JSX transformation for React components
